<?php
/**
 * Main Application Entry Point
 * Requires authentication to access the application
 */

require_once 'includes/auth_manager.php';

$auth = new AuthManager();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Get current user for display
$currentUser = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007cba">
    <title>Webpage Manager - Import & Analyze Forms</title>
    <link rel="stylesheet" href="assets/css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css?v=<?php echo time(); ?>">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="header-main">
                    <h1><i data-feather="code"></i> Webpage Manager</h1>
                    <p>Import, analyze, and manage webpages with form detection</p>
                </div>
                <div class="header-auth" id="header-auth">
                    <div class="auth-user-info">
                        <div class="user-avatar"><?php echo strtoupper(substr($currentUser['full_name'], 0, 1)); ?></div>
                        <div class="user-details">
                            <div class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></div>
                            <div class="user-role"><?php echo htmlspecialchars($currentUser['role']); ?></div>
                        </div>
                    </div>
                    <div class="auth-actions">
                        <a href="logout.php" class="auth-btn">
                            <i data-feather="log-out"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <nav class="nav-tabs" id="nav-tabs">
            <button class="tab-btn active" data-tab="import">
                <i data-feather="upload"></i>
                <span class="tab-text">Import</span>
            </button>
            <button class="tab-btn" data-tab="manage">
                <i data-feather="folder"></i>
                <span class="tab-text">Manage</span>
            </button>
            <button class="tab-btn" data-tab="forms">
                <i data-feather="file-text"></i>
                <span class="tab-text">Forms</span>
            </button>
            <button class="tab-btn" data-tab="database">
                <i data-feather="database"></i>
                <span class="tab-text">Database</span>
            </button>
            <button class="tab-btn" data-tab="sharing">
                <i data-feather="share-2"></i>
                <span class="tab-text">Share</span>
            </button>
            <?php if ($currentUser['role'] === 'admin'): ?>
            <button class="tab-btn" data-tab="users">
                <i data-feather="users"></i>
                <span class="tab-text">Users</span>
            </button>
            <?php endif; ?>
        </nav>

        <!-- Import Tab -->
        <div class="tab-content active" id="import-tab">
            <div class="upload-section">
                <h2>Import HTML Pages</h2>
                
                <div class="upload-mode-selector">
                    <label class="mode-option">
                        <input type="radio" name="upload-mode" value="single" checked>
                        <span class="mode-label">
                            <i data-feather="file"></i>
                            Single File Upload
                        </span>
                        <small>Upload one HTML file at a time</small>
                    </label>

                    <label class="mode-option">
                        <input type="radio" name="upload-mode" value="zip">
                        <span class="mode-label">
                            <i data-feather="archive"></i>
                            ZIP Archive Upload
                        </span>
                        <small>Upload a ZIP file containing multiple HTML files</small>
                    </label>
                </div>

                <!-- Project Selection -->
                <div class="project-selection">
                    <label for="project-input">
                        <i data-feather="folder"></i>
                        Project Name (Optional)
                    </label>
                    <div class="project-input-container">
                        <input type="text" id="project-input" class="project-input" placeholder="Enter project name or select existing" autocomplete="off">
                        <button type="button" id="project-dropdown-btn" class="project-dropdown-btn" title="Select from existing projects">
                            <i data-feather="chevron-down"></i>
                        </button>
                        <div id="project-dropdown" class="project-dropdown" style="display: none;">
                            <div class="project-dropdown-item" data-project="">
                                <i data-feather="folder"></i>
                                <span>No Project</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Single File Upload -->
                <div class="upload-container" id="single-upload">
                    <div class="upload-area" id="upload-area">
                        <div class="upload-content">
                            <i data-feather="upload-cloud" class="upload-icon"></i>
                            <h3>Drag & Drop HTML Files</h3>
                            <p>or click to browse and select files</p>
                            <div class="upload-info">
                                <small>Supported formats: .html, .htm</small>
                                <small>Maximum file size: 10MB</small>
                            </div>
                        </div>
                        <input type="file" id="file-input" accept=".html,.htm" multiple style="display: none;">
                    </div>
                </div>

                <!-- ZIP Upload -->
                <div class="upload-container" id="zip-upload" style="display: none;">
                    <div class="upload-area" id="zip-upload-area">
                        <div class="upload-content">
                            <i data-feather="archive" class="upload-icon"></i>
                            <h3>Upload ZIP Archive</h3>
                            <p>Select a ZIP file containing HTML files</p>
                            <div class="upload-info">
                                <small>Supported format: .zip</small>
                                <small>Maximum file size: 50MB</small>
                            </div>
                        </div>
                        <input type="file" id="zip-file-input" accept=".zip" style="display: none;">
                    </div>
                </div>

                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Uploading...</div>
                </div>

                <div class="upload-results" id="upload-results"></div>
            </div>
        </div>

        <!-- Manage Pages Tab -->
        <div class="tab-content" id="manage-tab">
            <div class="manage-section">
                <h2>Manage Pages</h2>

                <div class="manage-controls">
                    <div class="manage-controls-content">
                        <div class="filter-controls">
                            <div class="filter-group">
                                <label for="search-pages">Search Pages</label>
                                <input type="text" id="search-pages" placeholder="Type to search pages..." class="search-input">
                            </div>
                            <div class="filter-group">
                                <label for="status-filter">Status</label>
                                <select id="status-filter" class="status-filter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="project-filter">Project</label>
                                <select id="project-filter" class="project-filter">
                                    <option value="">All Projects</option>
                                </select>
                            </div>
                        </div>

                        <div class="action-controls">
                            <button id="refresh-pages" class="btn btn-secondary">
                                <i data-feather="refresh-cw"></i> Refresh
                            </button>
                            <button id="select-all-pages" class="btn btn-secondary">
                                <i data-feather="check-square"></i> Select All
                            </button>
                            <button id="bulk-actions-btn" class="btn btn-primary" style="display: none;">
                                <i data-feather="settings"></i> Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions Panel -->
                <div class="bulk-actions-panel" id="bulk-actions-panel" style="display: none;">
                    <div class="bulk-actions-content">
                        <span class="selected-count">0 pages selected</span>
                        <div class="bulk-buttons">
                            <button class="btn btn-warning" onclick="app.bulkArchive()">
                                <i data-feather="archive"></i> Archive
                            </button>
                            <button class="btn btn-success" onclick="app.bulkRestore()">
                                <i data-feather="rotate-ccw"></i> Restore
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(false)">
                                <i data-feather="trash-2"></i> Delete (Keep Files)
                            </button>
                            <button class="btn btn-danger" onclick="app.bulkDelete(true)">
                                <i data-feather="trash"></i> Delete (Remove Files)
                            </button>
                            <button class="btn btn-secondary" onclick="app.clearSelection()">
                                <i data-feather="x"></i> Clear Selection
                            </button>
                        </div>
                    </div>
                </div>

                <div class="pages-grid" id="pages-grid">
                    <!-- Pages will be loaded here -->
                </div>

                <!-- Page Statistics -->
                <div class="page-stats" id="page-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Pages:</span>
                        <span class="stat-value" id="total-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Active:</span>
                        <span class="stat-value" id="active-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Archived:</span>
                        <span class="stat-value" id="archived-pages">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Size:</span>
                        <span class="stat-value" id="total-size">0 MB</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forms Tab -->
        <div class="tab-content" id="forms-tab">
            <div class="forms-section">
                <h2>Form Management</h2>

                <div class="forms-controls">
                    <div class="page-selector">
                        <label for="page-select-forms">Select Page:</label>
                        <select id="page-select-forms" onchange="app.loadPageForms()">
                            <option value="">Choose a page...</option>
                        </select>
                        <button id="refresh-forms" class="btn btn-secondary" onclick="app.loadPageForms()">
                            <i data-feather="refresh-cw"></i> Refresh
                        </button>
                    </div>

                    <div class="form-actions">
                        <button id="save-all-forms" class="btn btn-primary" onclick="app.saveAllForms()">
                            <i data-feather="save"></i> Save All Changes
                        </button>
                        <button id="reset-forms" class="btn btn-secondary" onclick="app.resetForms()">
                            <i data-feather="rotate-ccw"></i> Reset Changes
                        </button>
                    </div>
                </div>

                <div class="forms-container" id="forms-container">
                    <div class="no-forms-message">
                        <i data-feather="file-text"></i>
                        <h3>No Forms Found</h3>
                        <p>Select a page to view and edit its forms, or import pages with forms to get started.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Tab -->
        <div class="tab-content" id="database-tab">
            <div class="database-section">
                <div class="database-nav">
                    <button class="db-nav-btn active" data-section="submissions">
                        <i data-feather="send"></i> Form Submissions
                    </button>
                    <button class="db-nav-btn" data-section="structure">
                        <i data-feather="grid"></i> Database Structure
                    </button>
                    <button class="db-nav-btn" data-section="analytics">
                        <i data-feather="bar-chart-2"></i> Analytics
                    </button>
                </div>

                <!-- Form Submissions Section -->
                <div class="db-section active" id="db-submissions-section">
                    <h2>Form Submissions</h2>
                    <div class="submissions-controls">
                        <div class="submissions-controls-content">
                            <div class="submissions-filters">
                                <div class="filter-group">
                                    <label for="submissions-page-filter">Page</label>
                                    <select id="submissions-page-filter">
                                        <option value="">All Pages</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="submissions-form-filter">Form</label>
                                    <select id="submissions-form-filter">
                                        <option value="">All Forms</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label for="submissions-date-from">From Date</label>
                                    <input type="date" id="submissions-date-from" class="date-input">
                                </div>
                                <div class="filter-group">
                                    <label for="submissions-date-to">To Date</label>
                                    <input type="date" id="submissions-date-to" class="date-input">
                                </div>
                                <div class="filter-group filter-actions">
                                    <label>&nbsp;</label>
                                    <button id="filter-submissions" class="btn btn-secondary">
                                        <i data-feather="filter"></i> Apply Filters
                                    </button>
                                </div>
                            </div>
                            <div class="submissions-actions">
                                <button id="export-submissions" class="btn btn-success">
                                    <i data-feather="download"></i> Export CSV
                                </button>
                                <button id="refresh-submissions" class="btn btn-secondary">
                                    <i data-feather="refresh-cw"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="submissions-container" id="submissions-container">
                        <div class="no-submissions-message">
                            <i data-feather="send"></i>
                            <h3>No Form Submissions</h3>
                            <p>Form submissions from shared pages will appear here.</p>
                        </div>
                    </div>
                </div>

                <!-- Database Structure Section -->
                <div class="db-section" id="db-structure-section">
                    <h2>Database Structure</h2>
                    <div class="db-actions">
                        <button id="generate-db" class="btn btn-primary">
                            <i data-feather="zap"></i> Generate Database Structure
                        </button>
                        <button id="export-sql" class="btn btn-secondary">
                            <i data-feather="download"></i> Export SQL
                        </button>
                    </div>
                    <div class="db-tables" id="db-tables">
                        <!-- Database tables will be shown here -->
                    </div>
                </div>



                <!-- Analytics Section -->
                <div class="db-section" id="db-analytics-section">
                    <h2>Analytics & Statistics</h2>
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h3>Total Submissions</h3>
                            <div class="analytics-value" id="total-submissions">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>This Month</h3>
                            <div class="analytics-value" id="monthly-submissions">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>Active Shares</h3>
                            <div class="analytics-value" id="active-shares">0</div>
                        </div>
                        <div class="analytics-card">
                            <h3>Total Views</h3>
                            <div class="analytics-value" id="total-views">0</div>
                        </div>
                    </div>
                    <div class="analytics-charts">
                        <div class="chart-container">
                            <h4>Submissions Over Time</h4>
                            <div id="submissions-chart" class="chart-placeholder">
                                Chart will be displayed here
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sharing Tab -->
        <div class="tab-content" id="sharing-tab">
            <div class="sharing-section">
                <h2>Page Sharing & URL Shortener</h2>

                <div class="sharing-actions">
                    <button id="create-share" class="btn btn-primary">
                        <i data-feather="share-2"></i> Create New Share
                    </button>
                    <button id="create-short-url" class="btn btn-secondary">
                        <i data-feather="link"></i> Shorten URL
                    </button>
                    <button id="refresh-shares" class="btn btn-secondary">
                        <i data-feather="refresh-cw"></i> Refresh
                    </button>
                </div>

                <div class="shares-container">
                    <h3>Active Shares</h3>
                    <div class="shares-grid" id="shares-grid">
                        <!-- Shares will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Tab (Admin Only) -->
        <div class="tab-content" id="users-tab">
            <div class="users-section">
                <h2>User Management</h2>

                <!-- Registration Settings -->
                <div class="registration-settings-section">
                    <div class="registration-settings-header">
                        <h3><i data-feather="settings"></i> Registration Settings</h3>
                        <div class="settings-status" id="settings-status">
                            <i data-feather="circle"></i> <span>Loading...</span>
                        </div>
                    </div>

                    <div class="registration-settings-content">
                        <div class="settings-grid">
                            <div class="setting-item">
                                <div class="setting-card">
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="allow-registration">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-info">
                                        <strong>Public Registration</strong>
                                        <p>Allow new users to create accounts through the registration page</p>
                                    </div>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-card">
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="require-approval">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <div class="setting-info">
                                        <strong>Admin Approval</strong>
                                        <p>Require administrator verification before new users can access the system</p>
                                    </div>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-card">
                                    <div class="setting-control">
                                        <select id="default-role" class="form-control" style="min-width: 120px;">
                                            <option value="viewer">Viewer</option>
                                            <option value="editor">Editor</option>
                                        </select>
                                    </div>
                                    <div class="setting-info">
                                        <strong>Default Role</strong>
                                        <p>Permission level automatically assigned to newly registered users</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="setting-item full-width">
                            <div class="setting-card">
                                <div class="setting-info" style="margin-bottom: 15px;">
                                    <strong>Welcome Message</strong>
                                    <p>Custom message displayed to users after successful registration</p>
                                </div>
                                <textarea id="welcome-message" class="form-control" rows="3" placeholder="Welcome! Your account is pending approval by an administrator." style="resize: vertical; font-family: inherit;"></textarea>
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 20px;">
                            <button id="reset-registration-settings" class="btn btn-secondary">
                                <i data-feather="rotate-ccw"></i> Reset
                            </button>
                            <button id="save-registration-settings" class="btn btn-primary">
                                <i data-feather="save"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pending Registrations -->
                <div class="pending-registrations-section">
                    <h3><i data-feather="clock"></i> Pending Registrations <span id="pending-count" class="badge">0</span></h3>
                    <div class="pending-registrations-container" id="pending-registrations">
                        <!-- Pending registrations will be loaded here -->
                    </div>
                </div>

                <!-- Active Users -->
                <div class="active-users-section">
                    <h3><i data-feather="users"></i> Active Users</h3>
                    <div class="users-actions">
                        <button id="create-user" class="btn btn-primary">
                            <i data-feather="user-plus"></i> Create User
                        </button>
                        <button id="refresh-users" class="btn btn-secondary">
                            <i data-feather="refresh-cw"></i> Refresh
                        </button>
                    </div>

                    <div class="users-container">
                        <div class="users-grid" id="users-grid">
                            <!-- Users will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Page Details -->
    <div class="modal" id="page-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Page Details</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Page details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Enhanced Modal for Creating Shares -->
    <div class="modal" id="share-modal">
        <div class="modal-content enhanced-share-modal">
            <div class="modal-header">
                <div class="modal-title-section">
                    <i data-feather="share-2" class="modal-icon"></i>
                    <h3>Create New Share</h3>
                </div>
                <button class="modal-close" onclick="app.closeShareModal()">
                    <i data-feather="x"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="share-form">
                    <!-- Page Selection Section -->
                    <div class="form-section">
                        <h4><i data-feather="file"></i> Page Selection</h4>
                        <div class="form-group">
                            <label for="share-page-select">Select Page to Share:</label>
                            <select id="share-page-select" required>
                                <option value="">Choose a page to share...</option>
                            </select>
                            <small class="form-help">Select the HTML page you want to share with others</small>
                        </div>
                    </div>

                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4><i data-feather="info"></i> Share Information</h4>
                        <div class="form-group">
                            <label for="share-title">Share Title:</label>
                            <input type="text" id="share-title" placeholder="Enter a custom title for the shared page">
                            <small class="form-help">This title will be displayed to visitors</small>
                        </div>

                        <div class="form-group">
                            <label for="share-description">Description:</label>
                            <textarea id="share-description" rows="3" placeholder="Optional description for the shared page"></textarea>
                            <small class="form-help">Provide context about what visitors can expect</small>
                        </div>
                    </div>

                    <!-- Form Behavior Section -->
                    <div class="form-section">
                        <h4><i data-feather="settings"></i> Form Behavior</h4>
                        <div class="form-group">
                            <label for="share-redirect-url">Redirect URL After Form Submission:</label>
                            <input type="text" id="share-redirect-url" placeholder="https://example.com/thank-you">
                            <small class="form-help">Optional: Redirect visitors to this URL after they submit a form</small>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="share-show-forms" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Enable Forms</strong>
                                    <small>Allow visitors to submit forms on the shared page</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Security & Access Section -->
                    <div class="form-section">
                        <h4><i data-feather="shield"></i> Security & Access</h4>
                        <div class="form-group">
                            <label for="share-password">Password Protection:</label>
                            <input type="password" id="share-password" placeholder="Leave empty for no password protection">
                            <small class="form-help">Optional: Require a password to access the shared page</small>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="share-expires">Expires At:</label>
                                <input type="datetime-local" id="share-expires">
                                <small class="form-help">Optional expiration date</small>
                            </div>
                            <div class="form-group">
                                <label for="share-max-views">Max Views:</label>
                                <input type="number" id="share-max-views" min="1" placeholder="Unlimited">
                                <small class="form-help">Limit number of views</small>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options Section -->
                    <div class="form-section">
                        <h4><i data-feather="sliders"></i> Advanced Options</h4>
                        <div class="checkbox-group">
                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="share-allow-download">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Allow Download</strong>
                                    <small>Let visitors download the page files</small>
                                </span>
                            </label>

                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="share-show-metadata">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Show Metadata</strong>
                                    <small>Display share information and view count</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="form-actions enhanced-actions">
                        <button type="submit" class="btn btn-primary enhanced-primary">
                            <i data-feather="share-2"></i>
                            <span>Create Share</span>
                        </button>
                        <button type="button" class="btn btn-secondary enhanced-secondary" onclick="app.closeShareModal()">
                            <i data-feather="x"></i>
                            <span>Cancel</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Enhanced Modal for URL Shortener -->
    <div class="modal" id="url-modal">
        <div class="modal-content enhanced-url-modal">
            <div class="modal-header">
                <div class="modal-title-section">
                    <i class="fas fa-link modal-icon"></i>
                    <h3>Create Short URL</h3>
                </div>
                <button class="modal-close" onclick="app.closeUrlModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="url-form">
                    <!-- URL Input Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-globe"></i> URL Information</h4>
                        <div class="form-group">
                            <label for="original-url">URL to Shorten:</label>
                            <input type="text" id="original-url" required placeholder="https://example.com/long-url">
                            <small class="form-help">Enter the full URL you want to create a short link for</small>
                        </div>
                    </div>

                    <!-- Customization Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-edit"></i> Customization</h4>
                        <div class="form-group">
                            <label for="url-title">Title:</label>
                            <input type="text" id="url-title" placeholder="Custom title for the short URL">
                            <small class="form-help">Optional: A descriptive title for this short URL</small>
                        </div>

                        <div class="form-group">
                            <label for="url-description">Description:</label>
                            <textarea id="url-description" rows="2" placeholder="Optional description"></textarea>
                            <small class="form-help">Optional: Additional details about this short URL</small>
                        </div>

                        <div class="form-group">
                            <label for="custom-short-code">Custom Short Code:</label>
                            <input type="text" id="custom-short-code" placeholder="e.g., PROMO2024" pattern="[A-Za-z0-9]{3,10}">
                            <small class="form-help">Optional: 3-10 characters, letters and numbers only. Leave empty for auto-generated code.</small>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-cogs"></i> Settings</h4>
                        <div class="form-group">
                            <label for="url-expires">Expires At:</label>
                            <input type="datetime-local" id="url-expires">
                            <small class="form-help">Optional: Set an expiration date for this short URL</small>
                        </div>

                        <div class="checkbox-group">
                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="url-track-clicks" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Track Clicks</strong>
                                    <small>Monitor how many times this short URL is accessed</small>
                                </span>
                            </label>

                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="url-show-preview">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Show Preview</strong>
                                    <small>Display a preview page before redirecting to the destination</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="form-actions enhanced-actions">
                        <button type="submit" class="btn btn-primary enhanced-primary">
                            <i class="fas fa-link"></i>
                            <span>Create Short URL</span>
                        </button>
                        <button type="button" class="btn btn-secondary enhanced-secondary" onclick="app.closeUrlModal()">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Creating Projects -->
    <div class="modal" id="project-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Project</h3>
                <button class="modal-close" onclick="app.closeProjectModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="project-form">
                    <div class="form-group">
                        <label for="project-name">Project Name:</label>
                        <input type="text" id="project-name" required placeholder="Enter project name">
                    </div>

                    <div class="form-group">
                        <label for="project-description">Description:</label>
                        <textarea id="project-description" rows="3" placeholder="Optional project description"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="project-color">Color:</label>
                        <input type="color" id="project-color" value="#667eea">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i data-feather="plus"></i> Create Project
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeProjectModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Enhanced Modal for Editing Shares -->
    <div class="modal" id="edit-share-modal">
        <div class="modal-content enhanced-edit-modal">
            <div class="modal-header">
                <div class="modal-title-section">
                    <i class="fas fa-edit modal-icon"></i>
                    <h3>Edit Share</h3>
                </div>
                <button class="modal-close" onclick="app.closeEditShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-share-form">
                    <input type="hidden" id="edit-share-id">

                    <!-- Share Information Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle"></i> Share Information</h4>
                        <div class="form-group">
                            <label for="edit-share-title">Share Title:</label>
                            <input type="text" id="edit-share-title" placeholder="Enter a custom title for the shared page">
                            <small class="form-help">Optional: This title will be displayed to visitors</small>
                        </div>

                        <div class="form-group">
                            <label for="edit-share-description">Description:</label>
                            <textarea id="edit-share-description" rows="3" placeholder="Optional description for the shared page"></textarea>
                            <small class="form-help">Provide context about what visitors can expect</small>
                        </div>
                    </div>

                    <!-- Security & Access Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-shield-alt"></i> Security & Access</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-share-password">Password Protection:</label>
                                <input type="password" id="edit-share-password" placeholder="Leave empty for no password">
                                <small class="form-help">Optional: Require a password to access the shared page</small>
                            </div>

                            <div class="form-group">
                                <label for="edit-share-expires">Expires At:</label>
                                <input type="datetime-local" id="edit-share-expires">
                                <small class="form-help">Optional: Set an expiration date</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit-share-max-views">Max Views:</label>
                            <select id="edit-share-max-views">
                                <option value="">Unlimited</option>
                                <option value="1">1 view</option>
                                <option value="5">5 views</option>
                                <option value="10">10 views</option>
                                <option value="25">25 views</option>
                                <option value="50">50 views</option>
                                <option value="100">100 views</option>
                            </select>
                            <small class="form-help">Limit number of views</small>
                        </div>
                    </div>

                    <!-- Form Behavior Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-cogs"></i> Form Behavior</h4>
                        <div class="form-group">
                            <label for="edit-share-redirect-url">Redirect URL After Form Submission:</label>
                            <input type="text" id="edit-share-redirect-url" placeholder="https://example.com/thank-you">
                            <small class="form-help">Optional: Redirect visitors to this URL after they submit a form</small>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="edit-share-show-forms" checked>
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Enable Forms</strong>
                                    <small>Allow visitors to submit forms on the shared page</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Advanced Options Section -->
                    <div class="form-section">
                        <h4><i class="fas fa-sliders-h"></i> Advanced Options</h4>
                        <div class="checkbox-group">
                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="edit-share-allow-download">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Allow Download</strong>
                                    <small>Let visitors download the page files</small>
                                </span>
                            </label>

                            <label class="enhanced-checkbox">
                                <input type="checkbox" id="edit-share-show-metadata">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">
                                    <strong>Show Metadata</strong>
                                    <small>Display share information and view count</small>
                                </span>
                            </label>
                        </div>
                    </div>

                    <div class="form-actions enhanced-actions">
                        <button type="submit" class="btn btn-primary enhanced-primary">
                            <i class="fas fa-save"></i>
                            <span>Update Share</span>
                        </button>
                        <button type="button" class="btn btn-secondary enhanced-secondary" onclick="app.closeEditShareModal()">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Delete Confirmation -->
    <div class="modal" id="delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="delete-modal-title">Confirm Deletion</h3>
                <button class="modal-close" onclick="app.closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i data-feather="alert-triangle"></i>
                    <p id="delete-modal-message">Are you sure you want to delete this page?</p>
                </div>

                <div class="delete-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="delete-files-checkbox" checked>
                        <span>Also delete associated files (CSS, JS, images)</span>
                        <small>Unchecking this will keep files on disk but remove from database</small>
                    </label>
                </div>

                <div class="delete-details" id="delete-details">
                    <!-- Details about what will be deleted -->
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">
                        <i data-feather="trash-2"></i> Delete
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="app.closeDeleteModal()">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for User Management -->
    <div class="modal" id="user-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="user-modal-title">Create User</h3>
                <button class="modal-close" onclick="app.closeUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="user-form">
                    <input type="hidden" id="user-id">

                    <div class="form-group">
                        <label for="user-username">Username:</label>
                        <input type="text" id="user-username" required placeholder="Enter username" pattern="[a-zA-Z0-9_]{3,50}">
                        <small class="form-help">3-50 characters, letters, numbers, and underscores only</small>
                    </div>

                    <div class="form-group">
                        <label for="user-email">Email:</label>
                        <input type="email" id="user-email" required placeholder="Enter email address">
                    </div>

                    <div class="form-group">
                        <label for="user-full-name">Full Name:</label>
                        <input type="text" id="user-full-name" required placeholder="Enter full name">
                    </div>

                    <div class="form-group">
                        <label for="user-role">Role:</label>
                        <select id="user-role" required>
                            <option value="viewer">Viewer - Can only view content</option>
                            <option value="editor" selected>Editor - Can edit and manage content</option>
                            <option value="admin">Admin - Full system access</option>
                        </select>
                    </div>

                    <div class="form-group" id="password-group">
                        <label for="user-password">Password:</label>
                        <input type="password" id="user-password" placeholder="Enter password" minlength="8">
                        <small class="form-help">Minimum 8 characters</small>
                    </div>

                    <div class="form-group" id="new-password-group" style="display: none;">
                        <label for="user-new-password">New Password:</label>
                        <input type="password" id="user-new-password" placeholder="Leave empty to keep current password" minlength="8">
                        <small class="form-help">Leave empty to keep current password</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="user-submit-btn">
                            <i data-feather="save"></i> Create User
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.closeUserModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    
    <script>
        // Set authentication state for JavaScript
        window.authUser = <?php echo json_encode($currentUser); ?>;
        window.isAuthenticated = true;
    </script>
    <script src="assets/js/app.js?v=<?php echo time(); ?>"></script>
    <script src="assets/js/mobile-enhancements.js"></script>
    <script>
        // Initialize Feather icons
        feather.replace();

        // Re-initialize Feather icons when content changes dynamically
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    feather.replace();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>