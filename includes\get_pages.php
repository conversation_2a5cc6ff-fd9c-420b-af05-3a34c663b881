<?php
require_once '../config/database.php';

header('Content-Type: application/json');

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get all pages with form counts and project information
    $sql = "SELECT
                p.id,
                p.filename,
                p.original_filename,
                p.title,
                p.description,
                p.file_path,
                p.file_size,
                p.file_hash,
                p.content_type,
                p.project_id,
                p.url_path,
                p.status,
                p.version,
                p.forms_detected,
                p.created_at,
                p.updated_at,
                pr.name as project_name,
                pr.color as project_color,
                COUNT(f.id) as forms_count
            FROM pages p
            LEFT JOIN forms f ON p.id = f.page_id
            LEFT JOIN projects pr ON p.project_id = pr.id
            WHERE p.status != 'deleted'
            GROUP BY p.id
            ORDER BY p.created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($pages);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
