<?php
/**
 * Database Connection and Schema Test
 * Run this to verify database setup and diagnose issues
 */

require_once 'config/database.php';

echo "<h2>Database Connection and Schema Test</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test required tables exist
    $requiredTables = ['users', 'projects', 'pages', 'forms', 'form_fields', 'form_submissions', 'associated_files', 'page_shares'];
    
    echo "<h3>Table Structure Check</h3>";
    
    foreach ($requiredTables as $table) {
        try {
            $sql = "DESCRIBE $table";
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Table: $table</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test data insertion
    echo "<h3>Data Insertion Test</h3>";
    
    // Test projects table
    try {
        $sql = "SELECT COUNT(*) as count FROM projects";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ Projects table: {$result['count']} records</p>";
        
        // Show project structure
        $sql = "SELECT id, name, slug FROM projects LIMIT 3";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($projects)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Slug</th></tr>";
            foreach ($projects as $project) {
                echo "<tr><td>{$project['id']}</td><td>{$project['name']}</td><td>{$project['slug']}</td></tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Projects table error: " . $e->getMessage() . "</p>";
    }
    
    // Test pages table
    try {
        $sql = "SELECT COUNT(*) as count FROM pages";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ Pages table: {$result['count']} records</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Pages table error: " . $e->getMessage() . "</p>";
    }
    
    // Test users table
    try {
        $sql = "SELECT COUNT(*) as count FROM users";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ Users table: {$result['count']} records</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Users table error: " . $e->getMessage() . "</p>";
    }
    
    // Test API endpoints
    echo "<h3>API Endpoint Test</h3>";
    
    // Test get_pages.php
    $pagesUrl = 'includes/get_pages.php';
    if (file_exists($pagesUrl)) {
        echo "<p>✅ get_pages.php exists</p>";
        
        // Test the endpoint
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5
            ]
        ]);
        
        $result = @file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $pagesUrl, false, $context);
        
        if ($result !== false) {
            $data = json_decode($result, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "<p>✅ get_pages.php returns valid JSON</p>";
                if (is_array($data)) {
                    echo "<p>📊 Pages API returned " . count($data) . " pages</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ get_pages.php returns invalid JSON: " . json_last_error_msg() . "</p>";
                echo "<pre>" . htmlspecialchars(substr($result, 0, 500)) . "</pre>";
            }
        } else {
            echo "<p style='color: red;'>❌ get_pages.php request failed</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ get_pages.php not found</p>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>If you see any red ❌ errors above, run the database_migration.sql script to fix them.</p>";
    echo "<p>If everything shows green ✅, your database is properly configured!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/database.php</p>";
}
?>
