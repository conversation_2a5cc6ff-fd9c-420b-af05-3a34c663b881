<?php
/**
 * Enhanced Form Submission Handler
 * Processes form submissions with full data collection and redirect support
 * Works both locally and when deployed online
 */

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

$isAjaxRequest = $isAjax;

// Set headers based on request type
if ($isAjaxRequest) {
    header('Content-Type: application/json');
}
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log script execution with detailed request info
error_log("enhanced_submit_form.php accessed at " . date('Y-m-d H:i:s') .
          " - Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN') .
          " - AJAX: " . ($isAjaxRequest ? 'yes' : 'no') .
          " - User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN') .
          " - Referer: " . ($_SERVER['HTTP_REFERER'] ?? 'NONE'));

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Method not allowed - Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN') .
              " - Expected: POST - URL: " . ($_SERVER['REQUEST_URI'] ?? 'UNKNOWN'));
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed - received ' . ($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN') . ' instead of POST']);
    exit;
}

require_once 'config/database.php';

// Log incoming POST data
error_log("POST data: " . json_encode($_POST));

/**
 * Get real IP address (works with proxies and load balancers)
 */
function getRealIpAddress() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
               'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 
               'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER)) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = explode(',', $ip)[0];
            }
            $ip = trim($ip);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * Parse user agent for browser and device information
 */
function parseUserAgent($userAgent) {
    $info = [
        'browser_name' => 'Unknown',
        'browser_version' => '',
        'os_name' => 'Unknown',
        'device_type' => 'desktop'
    ];
    
    if (preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Chrome';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Firefox';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Safari';
        $info['browser_version'] = $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches)) {
        $info['browser_name'] = 'Edge';
        $info['browser_version'] = $matches[1];
    }
    
    if (strpos($userAgent, 'Windows') !== false) {
        $info['os_name'] = 'Windows';
    } elseif (strpos($userAgent, 'Mac') !== false) {
        $info['os_name'] = 'macOS';
    } elseif (strpos($userAgent, 'Linux') !== false) {
        $info['os_name'] = 'Linux';
    } elseif (strpos($userAgent, 'Android') !== false) {
        $info['os_name'] = 'Android';
    } elseif (strpos($userAgent, 'iOS') !== false) {
        $info['os_name'] = 'iOS';
    }
    
    if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
        if (preg_match('/iPad|Tablet/', $userAgent)) {
            $info['device_type'] = 'tablet';
        } else {
            $info['device_type'] = 'mobile';
        }
    }
    
    return $info;
}

/**
 * Generate visitor session ID
 */
function generateVisitorSession($ip, $userAgent) {
    return hash('sha256', $ip . $userAgent . date('Y-m-d'));
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Database connection failed");
    }
    
    // Get form data
    $shareToken = $_POST['_share_token'] ?? null;
    $formId = $_POST['_form_id'] ?? null;
    $pageId = $_POST['_page_id'] ?? null;
    $formName = $_POST['_form_name'] ?? 'unnamed_form';
    
    if (!$shareToken && !$pageId) {
        throw new Exception("Share token or page ID is required");
    }
    
    // Validate share token and get share info
    $shareInfo = null;
    if ($shareToken) {
        $sql = "SELECT ps.*, p.id as page_id, p.title as page_title 
                FROM page_shares ps 
                JOIN pages p ON ps.page_id = p.id 
                WHERE ps.share_token = ? AND ps.is_active = 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$shareToken]);
        $shareInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$shareInfo) {
            throw new Exception("Invalid or expired share token");
        }
        
        if (!$shareInfo['show_forms']) {
            throw new Exception("Form submissions are disabled for this share");
        }
        
        if ($shareInfo['expires_at'] && strtotime($shareInfo['expires_at']) < time()) {
            throw new Exception("This share has expired");
        }
        
        if ($shareInfo['max_views'] && $shareInfo['view_count'] >= $shareInfo['max_views']) {
            throw new Exception("This share has reached its view limit");
        }
        
        $pageId = $shareInfo['page_id'];
    }
    
    if (!$pageId) {
        throw new Exception("Page ID is required");
    }
    
    $sql = "SELECT * FROM pages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$pageId]);
    $page = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$page) {
        throw new Exception("Page not found");
    }
    
    // Extract redirect URL
    $redirectUrl = $_POST['_redirect_url'] ?? null;
    $originalAction = $_POST['_original_action'] ?? null;

    // Prepare form data (exclude system fields)
    $formData = $_POST;
    $systemFields = ['_share_token', '_form_id', '_page_id', '_form_name', '_redirect_url', '_original_action'];
    foreach ($systemFields as $field) {
        unset($formData[$field]);
    }

    if (empty($_POST)) {
        throw new Exception("No POST data received");
    }

    // Log form data
    error_log("Form submission - Page ID: $pageId, Form fields: " . count($formData));
    
    // Get visitor information
    $ipAddress = getRealIpAddress();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referrer = $_SERVER['HTTP_REFERER'] ?? '';
    $userAgentInfo = parseUserAgent($userAgent);
    $visitorSession = generateVisitorSession($ipAddress, $userAgent);
    
    $submissionSource = 'shared';
    if ($shareInfo === null || strpos($referrer, $_SERVER['HTTP_HOST']) !== false) {
        $submissionSource = 'direct';
    }
    
    // Store form submission
    try {
        $sql = "DESCRIBE form_submissions";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');

        $dataColumn = 'submission_data';
        if (!in_array('submission_data', $columns) && in_array('form_data', $columns)) {
            $dataColumn = 'form_data';
        }
        error_log("Data column: $dataColumn");

        $insertColumns = ['page_id', $dataColumn];
        $insertValues = [$pageId, json_encode($formData)];
        $placeholders = ['?', '?'];

        $optionalColumns = [
            'form_id' => $formId,
            'share_id' => $shareInfo['id'] ?? null,
            'form_name' => $formName,
            'visitor_session' => $visitorSession,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'referrer' => $referrer,
            'browser_name' => $userAgentInfo['browser_name'],
            'browser_version' => $userAgentInfo['browser_version'],
            'os_name' => $userAgentInfo['os_name'],
            'device_type' => $userAgentInfo['device_type'],
            'submission_source' => $submissionSource
        ];

        foreach ($optionalColumns as $column => $value) {
            if (in_array($column, $columns)) {
                $insertColumns[] = $column;
                $insertValues[] = $value;
                $placeholders[] = '?';
            }
        }

        if (in_array('submitted_at', $columns)) {
            $insertColumns[] = 'submitted_at';
            $placeholders[] = 'NOW()';
        } elseif (in_array('created_at', $columns)) {
            $insertColumns[] = 'created_at';
            $placeholders[] = 'NOW()';
        }

        $sql = "INSERT INTO form_submissions (" . implode(', ', $insertColumns) . ")
                VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $db->prepare($sql);
        $stmt->execute($insertValues);
    } catch (PDOException $e) {
        error_log("Primary INSERT failed: " . $e->getMessage());
        $sql = "INSERT INTO form_submissions (page_id, $dataColumn) VALUES (?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$pageId, json_encode($formData)]);
    }
    
    $submissionId = $db->lastInsertId();
    
    // Log share access
    if ($shareInfo) {
        try {
            $sql = "SELECT id FROM page_shares WHERE id = ? AND is_active = 1";
            $stmt = $db->prepare($sql);
            $stmt->execute([$shareInfo['id']]);
            if ($stmt->fetch()) {
                $sql = "INSERT INTO share_access_log (
                            share_id, ip_address, user_agent, referrer,
                            access_type, accessed_at
                        ) VALUES (?, ?, ?, ?, 'form_submission', NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id'], $ipAddress, $userAgent, $referrer]);

                $sql = "UPDATE page_shares SET view_count = view_count + 1 WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$shareInfo['id']]);
            }
        } catch (PDOException $e) {
            error_log("Failed to log access: " . $e->getMessage());
        }
    }
    
    // Determine redirect URL with improved validation
    $finalRedirectUrl = null;

    // Helper function to validate and normalize URLs
    $validateUrl = function($url) {
        if (empty($url)) return null;

        // Allow full URLs
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }

        // Allow relative URLs (starting with / or ./)
        if (preg_match('/^(\/|\.\/|\.\.\/|[a-zA-Z0-9_-]+\.(html?|php))/', $url)) {
            return $url;
        }

        // Allow simple page names (add .html if no extension)
        if (preg_match('/^[a-zA-Z0-9_-]+$/', $url)) {
            return $url . '.html';
        }

        return null;
    };

    // Priority order: form-specific redirect > share redirect > original form action
    if (!empty($redirectUrl)) {
        $finalRedirectUrl = $validateUrl($redirectUrl);
    } elseif (!empty($shareInfo['redirect_url'])) {
        $finalRedirectUrl = $validateUrl($shareInfo['redirect_url']);
    } elseif (!empty($originalAction)) {
        $finalRedirectUrl = $validateUrl($originalAction);
    }

    // Handle response based on request type
    if ($isAjaxRequest) {
        // AJAX request - return JSON
        echo json_encode([
            'success' => true,
            'message' => 'Form submitted successfully',
            'submission_id' => $submissionId,
            'timestamp' => date('Y-m-d H:i:s'),
            'visitor_session' => $visitorSession,
            'redirect_url' => $finalRedirectUrl
        ]);
    } else {
        // Direct form submission - redirect or show success page
        if (!empty($finalRedirectUrl)) {
            header("Location: " . $finalRedirectUrl);
            exit;
        } else {
            // Show a simple success page
            echo "<!DOCTYPE html>
<html>
<head>
    <title>Form Submitted</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='success'>
        <h2>✅ Form Submitted Successfully!</h2>
        <p>Your form has been submitted and we have received your information.</p>
        <p>Submission ID: {$submissionId}</p>
        <p><a href='javascript:history.back()'>← Go Back</a></p>
    </div>
</body>
</html>";
        }
    }

} catch (Exception $e) {
    http_response_code(400);

    if ($isAjaxRequest) {
        // AJAX request - return JSON error
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        // Direct form submission - show error page
        echo "<!DOCTYPE html>
<html>
<head>
    <title>Form Submission Error</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='error'>
        <h2>❌ Form Submission Error</h2>
        <p>There was an error processing your form submission:</p>
        <p><strong>" . htmlspecialchars($e->getMessage()) . "</strong></p>
        <p><a href='javascript:history.back()'>← Go Back</a></p>
    </div>
</body>
</html>";
    }
}
?>