<?php
/**
 * Export Form Submissions to CSV
 * Exports all form submissions in CSV format
 */

require_once '../config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check if exporting a single submission
    $singleId = $_GET['id'] ?? null;

    if ($singleId) {
        // Get single submission
        $sql = "SELECT
                    fs.*,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    f.form_name,
                    ps.title as share_title
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                LEFT JOIN forms f ON fs.form_id = f.id
                LEFT JOIN page_shares ps ON fs.share_id = ps.id
                WHERE fs.id = ?
                ORDER BY fs.submitted_at DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute([$singleId]);
        $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($submissions)) {
            throw new Exception('Submission not found');
        }
    } else {
        // Get all submissions
        $sql = "SELECT
                    fs.*,
                    p.title as page_title,
                    p.original_filename as page_filename,
                    f.form_name,
                    ps.title as share_title
                FROM form_submissions fs
                LEFT JOIN pages p ON fs.page_id = p.id
                LEFT JOIN forms f ON fs.form_id = f.id
                LEFT JOIN page_shares ps ON fs.share_id = ps.id
                ORDER BY fs.submitted_at DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute();
        $submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Set headers for CSV download
    header('Content-Type: text/csv');

    if ($singleId) {
        header('Content-Disposition: attachment; filename="submission_' . $singleId . '_' . date('Y-m-d') . '.csv"');
    } else {
        header('Content-Disposition: attachment; filename="form_submissions_' . date('Y-m-d') . '.csv"');
    }

    header('Pragma: no-cache');
    header('Expires: 0');

    // Create output stream
    $output = fopen('php://output', 'w');

    // Collect all unique form fields
    $allFields = [];
    foreach ($submissions as $submission) {
        $formData = json_decode($submission['form_data'], true);
        if ($formData) {
            $allFields = array_merge($allFields, array_keys($formData));
        }
    }
    $allFields = array_unique($allFields);

    // Write CSV header
    $csvHeader = [
        'ID',
        'Submission Date',
        'Page Title',
        'Page Filename',
        'Form Name',
        'Source Type',
        'Share Title',
        'IP Address',
        'User Agent',
        'Referrer'
    ];

    // Add form fields to header
    foreach ($allFields as $field) {
        $csvHeader[] = 'Field: ' . $field;
    }

    fputcsv($output, $csvHeader);

    // Write data rows
    foreach ($submissions as $submission) {
        $formData = json_decode($submission['form_data'], true) ?: [];
        
        $row = [
            $submission['id'],
            $submission['submitted_at'],
            $submission['page_title'] ?: '',
            $submission['page_filename'] ?: '',
            $submission['form_name'] ?: '',
            $submission['share_id'] ? 'Shared' : 'Direct',
            $submission['share_title'] ?: '',
            $submission['ip_address'] ?: '',
            $submission['user_agent'] ?: '',
            $submission['referrer'] ?: ''
        ];

        // Add form field values
        foreach ($allFields as $field) {
            $row[] = $formData[$field] ?? '';
        }

        fputcsv($output, $row);
    }

    fclose($output);

} catch (Exception $e) {
    http_response_code(500);
    echo 'Error: ' . $e->getMessage();
}
?>
