<?php
/**
 * User Management API
 * Handles user CRUD operations for admin interface
 */

require_once 'auth_manager.php';

header('Content-Type: application/json');

$auth = new AuthManager();

// Check if user is logged in and is admin
if (!$auth->isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_users':
        getUserList();
        break;
        
    case 'create_user':
        createUser();
        break;
        
    case 'update_user':
        updateUser();
        break;
        
    case 'delete_user':
        deleteUser();
        break;
        
    case 'toggle_user_status':
        toggleUserStatus();
        break;

    case 'get_pending_registrations':
        getPendingRegistrations();
        break;

    case 'approve_registration':
        approveRegistration();
        break;

    case 'reject_registration':
        rejectRegistration();
        break;

    case 'get_registration_settings':
        getRegistrationSettings();
        break;

    case 'update_registration_settings':
        updateRegistrationSettings();
        break;

    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

function getUserList() {
    global $auth;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $sql = "SELECT id, username, email, full_name, role, is_active, last_login, created_at 
                FROM users 
                ORDER BY created_at DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format dates and add status info
        foreach ($users as &$user) {
            $user['created_at_formatted'] = date('M j, Y g:i A', strtotime($user['created_at']));
            $user['last_login_formatted'] = $user['last_login'] ? 
                date('M j, Y g:i A', strtotime($user['last_login'])) : 'Never';
            $user['status_text'] = $user['is_active'] ? 'Active' : 'Inactive';
            $user['role_text'] = ucfirst($user['role']);
        }
        
        echo json_encode(['success' => true, 'users' => $users]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function createUser() {
    global $auth;
    
    $userData = [
        'username' => trim($_POST['username'] ?? ''),
        'email' => trim($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'full_name' => trim($_POST['full_name'] ?? ''),
        'role' => $_POST['role'] ?? 'editor'
    ];
    
    $result = $auth->registerUser($userData);
    
    if ($result['success']) {
        http_response_code(201);
    } else {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

function updateUser() {
    global $auth;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $userId = $_POST['user_id'] ?? '';
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $fullName = trim($_POST['full_name'] ?? '');
        $role = $_POST['role'] ?? 'editor';
        $newPassword = $_POST['new_password'] ?? '';
        
        if (empty($userId) || empty($username) || empty($email) || empty($fullName)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'All fields are required']);
            return;
        }
        
        // Validate role
        if (!in_array($role, ['admin', 'editor', 'viewer'])) {
            $role = 'editor';
        }
        
        // Check if username or email already exists (excluding current user)
        $sql = "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$username, $email, $userId]);
        
        if ($stmt->fetch()) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Username or email already exists']);
            return;
        }
        
        // Update user
        if (!empty($newPassword)) {
            // Update with new password
            if (strlen($newPassword) < 8) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Password must be at least 8 characters long']);
                return;
            }
            
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET username = ?, email = ?, full_name = ?, role = ?, password_hash = ? WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$username, $email, $fullName, $role, $passwordHash, $userId]);
        } else {
            // Update without changing password
            $sql = "UPDATE users SET username = ?, email = ?, full_name = ?, role = ? WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$username, $email, $fullName, $role, $userId]);
        }
        
        echo json_encode(['success' => true, 'message' => 'User updated successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function deleteUser() {
    global $auth;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $userId = $_POST['user_id'] ?? '';
        $currentUser = $auth->getCurrentUser();
        
        if (empty($userId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            return;
        }
        
        // Prevent deleting own account
        if ($userId == $currentUser['id']) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Cannot delete your own account']);
            return;
        }
        
        // Check if user exists
        $sql = "SELECT username FROM users WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
            return;
        }
        
        // Delete user
        $sql = "DELETE FROM users WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        
        echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function toggleUserStatus() {
    global $auth;
    
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $userId = $_POST['user_id'] ?? '';
        $currentUser = $auth->getCurrentUser();
        
        if (empty($userId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            return;
        }
        
        // Prevent deactivating own account
        if ($userId == $currentUser['id']) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Cannot deactivate your own account']);
            return;
        }
        
        // Toggle user status
        $sql = "UPDATE users SET is_active = NOT is_active WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        
        if ($stmt->rowCount() > 0) {
            // Get new status
            $sql = "SELECT is_active FROM users WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            
            $status = $result['is_active'] ? 'activated' : 'deactivated';
            echo json_encode(['success' => true, 'message' => "User $status successfully"]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'User not found']);
        }
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function getPendingRegistrations() {
    global $auth;

    try {
        $database = new Database();
        $db = $database->getConnection();

        $sql = "SELECT id, username, email, full_name, role, registration_notes, created_at
                FROM users
                WHERE is_verified = 0 AND is_active = 0
                ORDER BY created_at DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute();
        $pendingUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'pending_registrations' => $pendingUsers,
            'total' => count($pendingUsers)
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function approveRegistration() {
    global $auth;

    $userId = $_POST['user_id'] ?? 0;

    if (!$userId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        return;
    }

    try {
        $database = new Database();
        $db = $database->getConnection();
        $currentUser = $auth->getCurrentUser();

        // Update user to verified and active
        $sql = "UPDATE users SET is_verified = 1, is_active = 1, verified_by = ?, verified_at = NOW() WHERE id = ? AND is_verified = 0";
        $stmt = $db->prepare($sql);
        $stmt->execute([$currentUser['id'], $userId]);

        if ($stmt->rowCount() > 0) {
            // Get user details for response
            $sql = "SELECT username, email, full_name FROM users WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'message' => "Registration approved for {$user['username']}",
                'user' => $user
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Pending registration not found']);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function rejectRegistration() {
    global $auth;

    $userId = $_POST['user_id'] ?? 0;

    if (!$userId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'User ID is required']);
        return;
    }

    try {
        $database = new Database();
        $db = $database->getConnection();

        // Get user details before deletion
        $sql = "SELECT username, email FROM users WHERE id = ? AND is_verified = 0";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Pending registration not found']);
            return;
        }

        // Delete the pending registration
        $sql = "DELETE FROM users WHERE id = ? AND is_verified = 0";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => "Registration rejected for {$user['username']}",
                'user' => $user
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Pending registration not found']);
        }

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function getRegistrationSettings() {
    global $auth;

    try {
        $database = new Database();
        $db = $database->getConnection();

        $sql = "SELECT setting_key, setting_value, setting_type, description
                FROM app_settings
                WHERE setting_key IN ('allow_user_registration', 'require_registration_approval', 'registration_default_role', 'registration_welcome_message')
                ORDER BY setting_key";

        $stmt = $db->prepare($sql);
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to key-value format
        $formattedSettings = [];
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            if ($setting['setting_type'] === 'boolean') {
                $value = (bool)$value;
            }
            $formattedSettings[$setting['setting_key']] = [
                'value' => $value,
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }

        echo json_encode([
            'success' => true,
            'settings' => $formattedSettings
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function updateRegistrationSettings() {
    global $auth;

    $settingsRaw = $_POST['settings'] ?? '';
    $settings = [];

    // Handle JSON string or array
    if (is_string($settingsRaw)) {
        $settings = json_decode($settingsRaw, true);
    } else {
        $settings = $settingsRaw;
    }

    if (empty($settings)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Settings data is required']);
        return;
    }

    try {
        $database = new Database();
        $db = $database->getConnection();

        $allowedSettings = [
            'allow_user_registration',
            'require_registration_approval',
            'registration_default_role',
            'registration_welcome_message'
        ];

        $db->beginTransaction();

        foreach ($settings as $key => $value) {
            if (in_array($key, $allowedSettings)) {
                // Convert boolean values
                if (is_bool($value)) {
                    $value = $value ? '1' : '0';
                }

                $sql = "UPDATE app_settings SET setting_value = ? WHERE setting_key = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$value, $key]);
            }
        }

        $db->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Registration settings updated successfully'
        ]);

    } catch (PDOException $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}
?>
