<?php
/**
 * Database Diagnostic Script
 * Checks if all required tables and columns exist
 */

require_once 'config/database.php';

try {
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Diagnostic Report</h2>";
    echo "<p>Database: " . DB_NAME . "</p>";
    echo "<p>Host: " . DB_HOST . "</p>";
    
    // Check if all required tables exist
    $requiredTables = [
        'users', 'projects', 'pages', 'forms', 'form_fields', 'form_submissions',
        'associated_files', 'generated_tables', 'page_shares', 'share_access_log',
        'short_urls', 'url_clicks', 'analysis_log', 'form_templates',
        'zip_extractions', 'page_versions', 'activity_log', 'app_settings'
    ];
    
    echo "<h3>Table Check:</h3>";
    $stmt = $db->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($requiredTables as $table) {
        $exists = in_array($table, $existingTables);
        echo "<p>✅ $table: " . ($exists ? "EXISTS" : "❌ MISSING") . "</p>";
    }
    
    // Check critical columns in key tables
    echo "<h3>Critical Column Check:</h3>";
    
    // Check page_shares table for short_code column
    try {
        $stmt = $db->query("DESCRIBE page_shares");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>page_shares.short_code: " . (in_array('short_code', $columns) ? "✅ EXISTS" : "❌ MISSING") . "</p>";
        echo "<p>page_shares columns: " . implode(', ', $columns) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ page_shares table: " . $e->getMessage() . "</p>";
    }
    
    // Check pages table for required columns
    try {
        $stmt = $db->query("DESCRIBE pages");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $requiredPageColumns = ['filename', 'url_path', 'forms_detected'];
        foreach ($requiredPageColumns as $col) {
            echo "<p>pages.$col: " . (in_array($col, $columns) ? "✅ EXISTS" : "❌ MISSING") . "</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ pages table: " . $e->getMessage() . "</p>";
    }
    
    // Check form_submissions table
    try {
        $stmt = $db->query("DESCRIBE form_submissions");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>form_submissions.submission_data: " . (in_array('submission_data', $columns) ? "✅ EXISTS" : "❌ MISSING") . "</p>";
        echo "<p>form_submissions columns: " . implode(', ', $columns) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ form_submissions table: " . $e->getMessage() . "</p>";
    }
    
    // Check analysis_log table for message column
    try {
        $stmt = $db->query("DESCRIBE analysis_log");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>analysis_log.message: " . (in_array('message', $columns) ? "✅ EXISTS" : "❌ MISSING") . "</p>";
        echo "<p>analysis_log columns: " . implode(', ', $columns) . "</p>";
    } catch (Exception $e) {
        echo "<p>❌ analysis_log table: " . $e->getMessage() . "</p>";
    }
    
    // Test a simple form submission simulation
    echo "<h3>Form Submission Test:</h3>";
    try {
        // Check if we can insert into form_submissions
        $testData = [
            'page_id' => 1,
            'form_name' => 'test_form',
            'submission_data' => json_encode(['test' => 'data']),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ];
        
        $sql = "INSERT INTO form_submissions (page_id, form_name, submission_data, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $result = $stmt->execute(array_values($testData));
        
        if ($result) {
            $testId = $db->lastInsertId();
            echo "<p>✅ Form submission test: SUCCESS (ID: $testId)</p>";
            
            // Clean up test data
            $db->prepare("DELETE FROM form_submissions WHERE id = ?")->execute([$testId]);
            echo "<p>✅ Test data cleaned up</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Form submission test failed: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Database Connection Failed</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
