<!DOCTYPE html>
<html>
<head>
    <title>Form Submission Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .info { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Form Submission Test</h1>
    
    <div class="info">
        <h3>Test Purpose:</h3>
        <p>This form tests if POST submissions work correctly on this hosting environment.</p>
        <p>It will help identify if the issue is with form submission or database processing.</p>
    </div>
    
    <h2>Test Form 1: Direct POST to enhanced_submit_form.php</h2>
    <form action="enhanced_submit_form.php" method="POST">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" rows="4">This is a test message to verify form submission works.</textarea>
        </div>
        
        <!-- Hidden fields that enhanced_submit_form.php expects -->
        <input type="hidden" name="_page_id" value="1">
        <input type="hidden" name="_form_name" value="test_form">
        <input type="hidden" name="_redirect_url" value="test_form.php?success=1">
        
        <button type="submit">Submit Test Form</button>
    </form>
    
    <h2>Test Form 2: Simple POST Test</h2>
    <form action="test_form.php" method="POST">
        <div class="form-group">
            <label for="simple_test">Simple Test:</label>
            <input type="text" id="simple_test" name="simple_test" value="POST Test" required>
        </div>
        
        <button type="submit">Submit Simple POST</button>
    </form>
    
    <?php
    // Handle simple POST test
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simple_test'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
        echo "<h3>✅ Simple POST Test Result:</h3>";
        echo "<p><strong>Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
        echo "<p><strong>Data Received:</strong> " . htmlspecialchars($_POST['simple_test']) . "</p>";
        echo "<p><strong>All POST Data:</strong></p>";
        echo "<pre>" . htmlspecialchars(print_r($_POST, true)) . "</pre>";
        echo "</div>";
    }
    
    // Show success message if redirected back
    if (isset($_GET['success'])) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
        echo "<h3>✅ Enhanced Form Submission Success!</h3>";
        echo "<p>The form was successfully submitted and redirected back here.</p>";
        echo "<p>This means the enhanced_submit_form.php is working correctly.</p>";
        echo "</div>";
    }
    ?>
    
    <h2>Environment Information</h2>
    <div class="info">
        <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>Request Method:</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></p>
        <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?></p>
        <p><strong>HTTP Host:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Unknown'; ?></p>
    </div>
    
    <h2>Instructions</h2>
    <div class="info">
        <ol>
            <li><strong>First, run debug_database.php</strong> to check database structure</li>
            <li><strong>Test Form 1:</strong> Submit to see if enhanced_submit_form.php works</li>
            <li><strong>Test Form 2:</strong> Submit to verify basic POST functionality</li>
            <li><strong>Check browser console</strong> for any JavaScript errors</li>
            <li><strong>Check server error logs</strong> for detailed error information</li>
        </ol>
    </div>
</body>
</html>
