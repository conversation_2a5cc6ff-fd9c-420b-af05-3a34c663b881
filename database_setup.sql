-- Webpage Manager Database Setup - SHARED HOSTING VERSION
-- Complete database schema for shared hosting environments
-- Version: 2.0.0
-- Created: 2025-01-27

-- =============================================================================
-- SHARED HOSTING INSTALLATION INSTRUCTIONS
-- =============================================================================
-- 1. Create a database through your hosting control panel (cPanel, Plesk, etc.)
-- 2. Create a database user and assign it to the database with full privileges
-- 3. Note down: database name, username, password, and host (usually localhost)
-- 4. Update config/database.php with these details
-- 5. Import this SQL file using phpMyAdmin, Adminer, or your hosting provider's database tool
-- 6. All tables will be created automatically

-- =============================================================================
-- TABLE CREATION
-- =============================================================================

-- Disable foreign key checks for setup
SET FOREIGN_KEY_CHECKS = 0;

-- Application settings table
CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Users table (for multi-user support)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    role ENUM('admin', 'editor', 'viewer') DEFAULT 'editor',
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(64) NULL,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    registration_notes TEXT NULL,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_is_verified (is_verified),
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Projects table (for organizing pages into projects)
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    slug VARCHAR(255) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#667eea',
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_slug (slug),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Pages table (stores uploaded HTML pages) - MATCHES RUNNING DATABASE
CREATE TABLE IF NOT EXISTS pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT DEFAULT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    title VARCHAR(500),
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    content_type VARCHAR(100),
    url_path VARCHAR(255),
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    version INT DEFAULT 1,
    forms_detected INT DEFAULT 0,
    meta_keywords TEXT,
    meta_description TEXT,
    language VARCHAR(10) DEFAULT 'en',
    charset VARCHAR(50) DEFAULT 'UTF-8',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_filename (filename),
    INDEX idx_project_id (project_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_file_hash (file_hash),
    INDEX idx_url_path (url_path),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Forms table (stores detected form structures) - MATCHES config/database.php
CREATE TABLE IF NOT EXISTS forms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    form_name VARCHAR(255),
    form_action VARCHAR(500),
    form_method VARCHAR(10) DEFAULT 'GET',
    form_enctype VARCHAR(100),
    form_id VARCHAR(255),
    form_class VARCHAR(255),
    form_target VARCHAR(50),
    form_autocomplete ENUM('on', 'off') DEFAULT 'on',
    form_novalidate BOOLEAN DEFAULT FALSE,
    form_index INT NOT NULL DEFAULT 0,
    form_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    validation_rules JSON,
    custom_attributes JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    INDEX idx_page_id (page_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Form fields table (stores individual form field details) - MATCHES ACTUAL DATABASE
CREATE TABLE IF NOT EXISTS form_fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    field_id VARCHAR(255),
    field_class VARCHAR(255),
    field_placeholder VARCHAR(500),
    field_value TEXT,
    field_required BOOLEAN DEFAULT FALSE,
    field_readonly BOOLEAN DEFAULT FALSE,
    field_disabled BOOLEAN DEFAULT FALSE,
    field_multiple BOOLEAN DEFAULT FALSE,
    field_min VARCHAR(50),
    field_max VARCHAR(50),
    field_step VARCHAR(50),
    field_pattern VARCHAR(500),
    field_maxlength INT,
    field_minlength INT,
    field_size INT,
    field_rows INT,
    field_cols INT,
    field_accept VARCHAR(255),
    field_autocomplete VARCHAR(100),
    field_options TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    field_label VARCHAR(500),
    field_validation_rules JSON,
    field_custom_attributes JSON,
    field_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
    INDEX idx_form_id (form_id),
    INDEX idx_field_name (field_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Page shares table (for secure page sharing) - MOVED BEFORE form_submissions
CREATE TABLE IF NOT EXISTS page_shares (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    share_token VARCHAR(32) NOT NULL UNIQUE,
    title VARCHAR(255),
    description TEXT,
    password_hash VARCHAR(255) NULL,
    max_views INT NULL,
    current_views INT DEFAULT 0,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    allow_submissions BOOLEAN DEFAULT TRUE,
    collect_analytics BOOLEAN DEFAULT TRUE,
    custom_message TEXT,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_share_token (share_token),
    INDEX idx_page_id (page_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Form submissions table (stores collected form data) - MATCHES config/database.php
CREATE TABLE IF NOT EXISTS form_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    form_id INT NULL,
    share_id INT NULL,
    form_name VARCHAR(255),
    submission_data JSON NOT NULL,
    visitor_session VARCHAR(64),
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    country_code VARCHAR(2),
    city VARCHAR(100),
    browser_name VARCHAR(50),
    browser_version VARCHAR(20),
    os_name VARCHAR(50),
    device_type ENUM('desktop', 'mobile', 'tablet') DEFAULT 'desktop',
    submission_source ENUM('direct', 'shared', 'embedded') DEFAULT 'shared',
    status ENUM('pending', 'processed', 'archived', 'spam', 'reviewed') DEFAULT 'pending',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    tags JSON,
    notes TEXT,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    response_sent BOOLEAN DEFAULT FALSE,
    response_sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_page_id (page_id),
    INDEX idx_form_id (form_id),
    INDEX idx_share_id (share_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_submission_source (submission_source),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Associated files table (tracks uploaded assets) - MATCHES ACTUAL DATABASE
CREATE TABLE IF NOT EXISTS associated_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    is_referenced BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    INDEX idx_page_id (page_id),
    INDEX idx_file_type (file_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Generated tables metadata (for database structure generation)
CREATE TABLE IF NOT EXISTS generated_tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(255) NOT NULL,
    form_id INT NOT NULL,
    sql_structure TEXT NOT NULL,
    field_mappings JSON,
    is_created BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
    INDEX idx_table_name (table_name),
    INDEX idx_form_id (form_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- Share access log (tracks share usage)
CREATE TABLE IF NOT EXISTS share_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    share_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    access_type ENUM('view', 'submit') DEFAULT 'view',
    country VARCHAR(2),
    city VARCHAR(100),
    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (share_id) REFERENCES page_shares(id) ON DELETE CASCADE,
    INDEX idx_share_id (share_id),
    INDEX idx_accessed_at (accessed_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- URL shortener table (for general URL shortening)
CREATE TABLE IF NOT EXISTS short_urls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_url TEXT NOT NULL,
    short_code VARCHAR(10) NOT NULL UNIQUE,
    title VARCHAR(255),
    description TEXT,
    click_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    created_by INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_short_code (short_code),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Click tracking for short URLs
CREATE TABLE IF NOT EXISTS url_clicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    short_url_id INT DEFAULT NULL,
    share_id INT DEFAULT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer VARCHAR(500),
    country VARCHAR(2),
    city VARCHAR(100),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (short_url_id) REFERENCES short_urls(id) ON DELETE CASCADE,
    FOREIGN KEY (share_id) REFERENCES page_shares(id) ON DELETE CASCADE,
    INDEX idx_short_url_id (short_url_id),
    INDEX idx_share_id (share_id),
    INDEX idx_clicked_at (clicked_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Analysis log table (for tracking page analysis) - MATCHES config/database.php
CREATE TABLE IF NOT EXISTS analysis_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT DEFAULT NULL,
    form_id INT DEFAULT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    message TEXT,
    details JSON,
    execution_time DECIMAL(10,4) DEFAULT NULL,
    memory_usage INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
    INDEX idx_page_id (page_id),
    INDEX idx_form_id (form_id),
    INDEX idx_analysis_type (analysis_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Form templates table (for reusable form structures) - MATCHES config/database.php
CREATE TABLE IF NOT EXISTS form_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    template_data JSON NOT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ZIP extraction log table (for tracking ZIP file imports)
CREATE TABLE IF NOT EXISTS zip_extractions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_filename VARCHAR(255) NOT NULL,
    extracted_path VARCHAR(500) NOT NULL,
    total_files INT DEFAULT 0,
    html_files INT DEFAULT 0,
    asset_files INT DEFAULT 0,
    extraction_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_original_filename (original_filename),
    INDEX idx_extraction_status (extraction_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Page versions table (for version control)
CREATE TABLE IF NOT EXISTS page_versions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    version_number INT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    changes_summary TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    UNIQUE KEY uk_page_version (page_id, version_number),
    INDEX idx_page_id (page_id),
    INDEX idx_version_number (version_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Activity log table (for tracking user actions)
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT 1,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default registration settings
INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, description, is_system) VALUES
('allow_user_registration', '0', 'boolean', 'Allow public user registration', 1),
('require_registration_approval', '1', 'boolean', 'Require admin approval for new registrations', 1),
('registration_default_role', 'viewer', 'string', 'Default role for new registrations', 1),
('registration_welcome_message', 'Welcome! Your account is pending approval by an administrator.', 'string', 'Message shown after registration', 1);

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================================================
-- SETUP COMPLETE
-- =============================================================================
-- Your database is now ready for the Webpage Manager application!
-- 
-- Next steps:
-- 1. Update config/database.php with your database connection details
-- 2. Upload the application files to your web server
-- 3. Access the application in your browser to create your first admin user
-- 4. Start uploading and managing your web pages!
