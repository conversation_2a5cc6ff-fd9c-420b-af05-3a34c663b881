<?php
/**
 * Login Page
 * User authentication interface following the application's design system
 */

require_once 'includes/auth_manager.php';

$auth = new AuthManager();
$error = '';
$success = '';
$requireSetup = false;

// Redirect if already logged in
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'login') {
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                $error = 'Please enter both username/email and password';
            } else {
                $result = $auth->login($username, $password);
                
                if ($result['success']) {
                    // Redirect to intended page or dashboard
                    $redirect = $_GET['redirect'] ?? 'index.php';
                    header("Location: $redirect");
                    exit;
                } else {
                    $error = $result['message'];
                    if (isset($result['require_setup'])) {
                        $requireSetup = true;
                    }
                }
            }
        } elseif ($_POST['action'] === 'setup_admin') {
            $password = $_POST['admin_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            if (empty($password) || empty($confirmPassword)) {
                $error = 'Please enter and confirm the admin password';
            } elseif ($password !== $confirmPassword) {
                $error = 'Passwords do not match';
            } else {
                $result = $auth->setupAdminPassword($password);
                
                if ($result['success']) {
                    $success = 'Admin password set successfully. You can now log in.';
                    $requireSetup = false;
                } else {
                    $error = $result['message'];
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007cba">
    <title>Login - Webpage Manager</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
    <style>
        .login-container {
            max-width: 400px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.875rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .alert {
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .alert-success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .setup-form {
            display: none;
        }
        
        .setup-form.active {
            display: block;
        }
        
        .back-link {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .password-requirements {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i data-feather="code"></i> Webpage Manager</h1>
            <p>Import, analyze, and manage webpages with form detection</p>
        </header>

        <div class="login-container">
            <?php if (!$requireSetup): ?>
                <!-- Regular Login Form -->
                <div class="login-header">
                    <h1><i data-feather="log-in"></i> Sign In</h1>
                    <p>Enter your credentials to access the system</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i data-feather="alert-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i data-feather="check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <input type="hidden" name="action" value="login">
                    
                    <div class="form-group">
                        <label for="username">
                            <i data-feather="user"></i> Username or Email
                        </label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            required 
                            autocomplete="username"
                            value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                        >
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i data-feather="lock"></i> Password
                        </label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required 
                            autocomplete="current-password"
                        >
                    </div>

                    <button type="submit" class="login-btn">
                        <i data-feather="log-in"></i> Sign In
                    </button>
                </form>

                <!-- Registration Link -->
                <div class="register-link" style="text-align: center; margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid #e1e5e9;">
                    <p style="color: #666; margin-bottom: 0.5rem;">Don't have an account?</p>
                    <a href="register.php" style="color: #667eea; text-decoration: none; font-weight: 500;">
                        <i data-feather="user-plus"></i> Create Account
                    </a>
                </div>

            <?php else: ?>
                <!-- Admin Setup Form -->
                <div class="login-header">
                    <h1><i data-feather="settings"></i> Admin Setup</h1>
                    <p>Set up your administrator password</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i data-feather="alert-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <input type="hidden" name="action" value="setup_admin">
                    
                    <div class="form-group">
                        <label for="admin_password">
                            <i data-feather="lock"></i> New Admin Password
                        </label>
                        <input 
                            type="password" 
                            id="admin_password" 
                            name="admin_password" 
                            required 
                            minlength="8"
                        >
                        <div class="password-requirements">
                            Password must be at least 8 characters long
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">
                            <i data-feather="lock"></i> Confirm Password
                        </label>
                        <input 
                            type="password" 
                            id="confirm_password" 
                            name="confirm_password" 
                            required 
                            minlength="8"
                        >
                    </div>

                    <button type="submit" class="login-btn">
                        <i data-feather="save"></i> Set Password
                    </button>
                </form>
            <?php endif; ?>


        </div>
    </div>

    <script>
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const inputs = form.querySelectorAll('input[required]');
                    let isValid = true;
                    
                    inputs.forEach(input => {
                        if (!input.value.trim()) {
                            isValid = false;
                            input.style.borderColor = '#e74c3c';
                        } else {
                            input.style.borderColor = '#e1e5e9';
                        }
                    });
                    
                    // Check password confirmation if present
                    const password = form.querySelector('input[name="admin_password"]');
                    const confirm = form.querySelector('input[name="confirm_password"]');
                    
                    if (password && confirm) {
                        if (password.value !== confirm.value) {
                            isValid = false;
                            confirm.style.borderColor = '#e74c3c';
                            alert('Passwords do not match');
                        }
                    }
                    
                    if (!isValid) {
                        e.preventDefault();
                    }
                });
            });
            
            // Clear error styling on input
            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('input', function() {
                    this.style.borderColor = '#e1e5e9';
                });
            });
        });

        // Initialize Feather icons
        feather.replace();
    </script>
</body>
</html>
