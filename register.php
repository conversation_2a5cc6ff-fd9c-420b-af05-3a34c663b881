<?php
/**
 * Public Registration Page
 * Allows users to register when registration is enabled
 */

require_once 'includes/auth_manager.php';
require_once 'includes/deployment_config.php';

$auth = new AuthManager();
$config = new DeploymentConfig();
$error = '';
$success = '';
$registrationClosed = false;

// Check if registration is enabled
if (!$config->get('allow_user_registration', false)) {
    $registrationClosed = true;
}

// Redirect if already logged in
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$registrationClosed) {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $fullName = trim($_POST['full_name'] ?? '');
    $registrationNotes = trim($_POST['registration_notes'] ?? '');
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($fullName)) {
        $error = 'Please fill in all required fields';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        // Create user account (pending verification)
        $userData = [
            'username' => $username,
            'email' => $email,
            'password' => $password,
            'full_name' => $fullName,
            'role' => $config->get('registration_default_role', 'viewer'),
            'registration_notes' => $registrationNotes
        ];
        
        $result = $auth->registerUserPending($userData);
        
        if ($result['success']) {
            $success = $config->get('registration_welcome_message', 'Registration successful! Your account is pending approval.');
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007cba">
    <title>Register - Webpage Manager</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/mobile-enhancements.css">
    <script src="https://unpkg.com/feather-icons"></script>
    <style>
        .register-container {
            max-width: 500px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .register-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            border: 1px solid #e2e8f0;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-header h1 {
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .register-header p {
            color: #718096;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #4a5568;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007cba;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-help {
            font-size: 0.875rem;
            color: #718096;
            margin-top: 0.25rem;
        }
        
        .register-btn {
            width: 100%;
            padding: 0.875rem;
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 124, 186, 0.3);
        }
        
        .register-btn:active {
            transform: translateY(0);
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .login-link a {
            color: #007cba;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .alert-error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }
        
        .alert-success {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }
        
        .alert-warning {
            background: #fefcbf;
            color: #d69e2e;
            border: 1px solid #faf089;
        }
        
        .closed-message {
            text-align: center;
            padding: 2rem;
        }
        
        .closed-message h2 {
            color: #4a5568;
            margin-bottom: 1rem;
        }
        
        .closed-message p {
            color: #718096;
            margin-bottom: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .register-container {
                margin: 1rem auto;
            }
            
            .register-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <?php if ($registrationClosed): ?>
                <div class="closed-message">
                    <h2><i data-feather="user-x"></i> Registration Closed</h2>
                    <p>User registration is currently disabled. Please contact an administrator for access.</p>
                    <a href="login.php" class="register-btn">
                        <i data-feather="log-in"></i> Go to Login
                    </a>
                </div>
            <?php else: ?>
                <div class="register-header">
                    <h1><i data-feather="user-plus"></i> Create Account</h1>
                    <p>Join the Webpage Manager platform</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i data-feather="alert-circle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i data-feather="check-circle"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php else: ?>
                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="username">
                                <i data-feather="user"></i> Username *
                            </label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                required 
                                autocomplete="username"
                                value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                            >
                            <div class="form-help">Choose a unique username (letters, numbers, underscore only)</div>
                        </div>

                        <div class="form-group">
                            <label for="email">
                                <i data-feather="mail"></i> Email Address *
                            </label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                required 
                                autocomplete="email"
                                value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                            >
                        </div>

                        <div class="form-group">
                            <label for="full_name">
                                <i data-feather="user"></i> Full Name *
                            </label>
                            <input 
                                type="text" 
                                id="full_name" 
                                name="full_name" 
                                required 
                                autocomplete="name"
                                value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                            >
                        </div>

                        <div class="form-group">
                            <label for="password">
                                <i data-feather="lock"></i> Password *
                            </label>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                autocomplete="new-password"
                            >
                            <div class="form-help">Minimum 8 characters</div>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">
                                <i data-feather="lock"></i> Confirm Password *
                            </label>
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                required 
                                autocomplete="new-password"
                            >
                        </div>

                        <div class="form-group">
                            <label for="registration_notes">
                                <i data-feather="message-square"></i> Additional Information
                            </label>
                            <textarea 
                                id="registration_notes" 
                                name="registration_notes" 
                                placeholder="Tell us why you'd like access (optional)"
                            ><?php echo htmlspecialchars($_POST['registration_notes'] ?? ''); ?></textarea>
                            <div class="form-help">This helps administrators review your request</div>
                        </div>

                        <button type="submit" class="register-btn">
                            <i data-feather="user-plus"></i> Create Account
                        </button>
                    </form>
                <?php endif; ?>

                <div class="login-link">
                    Already have an account? <a href="login.php">Sign in here</a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        feather.replace();
    </script>
</body>
</html>
