# Webpage Manager v2.0

A comprehensive web application for managing HTML pages, forms, and data collection with advanced features including user authentication, page sharing, and analytics.

**Developer**: Defabrika
**Version**: 2.0.0
**Last Updated**: 2025-01-27

## 🚀 Features

### Core Functionality
- **📄 Page Management**: Upload, organize, and manage HTML pages with associated assets
- **📝 Form Detection**: Automatically detect and analyze forms in uploaded HTML pages
- **📊 Data Collection**: Collect and manage form submissions with detailed analytics
- **📁 Project Organization**: Group pages into projects for better organization
- **📦 ZIP Import**: Bulk import of HTML pages and assets from ZIP archives

### Advanced Features
- **👥 User Authentication**: Multi-user support with role-based access control (Admin, Editor, Viewer)
- **🔗 Page Sharing**: Create secure, time-limited shares of pages with custom access controls
- **🔗 URL Shortener**: Built-in URL shortening service for easy link sharing
- **📈 Analytics Dashboard**: Comprehensive analytics with visual charts and activity tracking
- **🗄️ Database Management**: Dynamic database structure generation and SQL export
- **⚙️ Admin Controls**: User registration management with approval workflows

### Security & Access Control
- **🔐 Role-Based Permissions**: Different access levels for different user types
- **🛡️ Secure Sharing**: Token-based page sharing with expiration dates
- **✅ User Registration**: Admin-controlled user registration with approval workflow
- **🔒 Session Management**: Secure login/logout with session handling

## 📋 Prerequisites

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache/Nginx
- **Browser**: Modern web browser with JavaScript enabled

## 🛠️ Installation

### Option A: Local/VPS Environment (Full Control)

#### 1. Database Setup
```sql
-- Create database (if you have privileges)
CREATE DATABASE webpage_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u username -p webpage_manager < database_setup.sql
```

#### 2. Configuration
Update `config/database.php` with your database credentials:
```php
<?php
class Database {
    private $host = 'localhost';
    private $db_name = 'webpage_manager';
    private $username = 'your_username';
    private $password = 'your_password';
    // ...
}
```

### Option B: Shared Hosting Environment

#### 1. Database Setup via Control Panel
1. **Create Database**: Use cPanel, Plesk, or your hosting provider's control panel
2. **Create Database User**: Create a user and assign full privileges to the database
3. **Note Details**: Save database name, username, password, and host (usually `localhost`)

#### 2. Import Database Schema
- **Method 1 - phpMyAdmin**:
  1. Access phpMyAdmin from your hosting control panel
  2. Select your database
  3. Go to "Import" tab
  4. Upload `database_setup_shared_hosting.sql`
  5. Click "Go" to import

- **Method 2 - File Manager**:
  1. Upload `database_setup_shared_hosting.sql` to your hosting account
  2. Use your hosting provider's database import tool
  3. Select the uploaded SQL file and import

#### 3. Configuration for Shared Hosting
Update `config/database.php` with your hosting provider's details:
```php
<?php
class Database {
    private $host = 'localhost';  // Or your hosting provider's DB host
    private $db_name = 'your_hosting_db_name';  // From control panel
    private $username = 'your_hosting_db_user'; // From control panel
    private $password = 'your_hosting_db_pass'; // From control panel
    // ...
}
```

### Universal Steps (Both Environments)

#### File Permissions
```bash
# Make uploads directory writable (or use File Manager in hosting control panel)
chmod 755 uploads/
chmod 755 uploads/pages/
chmod 755 uploads/assets/
chmod 755 uploads/projects/
chmod 755 uploads/temp/
```

#### Initial Setup
1. Upload all application files to your web server
2. Access the application in your web browser
3. Complete the initial setup if prompted
4. Create your first admin user account

## 🎯 Quick Start Guide

### For Administrators
1. **Login** with admin credentials
2. **Configure Settings**: Go to Users tab → Registration Settings
3. **Manage Users**: Approve registrations, set roles
4. **Upload Content**: Add HTML pages and assets
5. **Monitor Analytics**: View submission data and trends

### For Editors
1. **Upload Pages**: Use the upload interface for HTML files or ZIP archives
2. **Manage Forms**: View detected forms and configure settings
3. **Share Pages**: Create secure shares with custom permissions
4. **Export Data**: Download form submissions and analytics

### For Viewers
1. **Browse Pages**: View uploaded pages and their details
2. **View Analytics**: Access read-only analytics and reports
3. **Access Shares**: View shared pages (if permitted)

## 📱 User Interface

### Main Navigation
- **📄 Manage**: Page upload, organization, and management
- **📝 Forms**: Form detection, configuration, and editing
- **🗄️ Database**: Form submissions, analytics, and database tools
- **🔗 Sharing**: Page sharing and URL shortening
- **👥 Users**: User management and registration controls (Admin only)

### Key Features by Tab

#### Manage Tab
- Drag-and-drop file upload
- ZIP archive support
- Project-based organization
- Bulk page operations
- Search and filtering

#### Forms Tab
- Automatic form detection
- Form field analysis
- Form modification tools
- Validation rule setup

#### Database Tab
- **Form Submissions**: View, filter, export, and delete submission data
- **Database Structure**: Generate database schemas from forms
- **Analytics**: Visual charts, top pages, recent activity

#### Sharing Tab
- Create secure page shares
- Set expiration dates and permissions
- Generate short URLs
- Monitor share analytics

#### Users Tab (Admin Only)
- User account management
- Registration approval workflow
- Role assignment
- Registration settings control

## 🔧 Technical Architecture

### Frontend
- **HTML5/CSS3**: Modern responsive design
- **JavaScript ES6+**: Modular, event-driven architecture
- **Feather Icons**: Lightweight SVG icon system
- **Local Storage**: State persistence across sessions

### Backend
- **PHP 7.4+**: Object-oriented architecture with PDO
- **MySQL**: Relational database with foreign key constraints
- **RESTful APIs**: JSON-based internal API endpoints
- **File Handling**: Secure upload and processing system

### Database Schema
```
users (authentication & roles)
├── pages (HTML page storage)
│   ├── forms (detected form definitions)
│   │   └── form_submissions (collected data)
│   └── page_shares (secure sharing)
├── projects (page organization)
├── short_urls (URL shortening)
└── app_settings (configuration)
```

## 🔒 Security Features

- **Password Security**: bcrypt hashing with salt
- **SQL Injection Prevention**: Prepared statements throughout
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Token-based form validation
- **Access Control**: Role-based permissions system
- **Secure Sessions**: HTTP-only cookies with regeneration
- **File Upload Security**: Type validation and secure storage

## 📊 Analytics & Reporting

### Dashboard Metrics
- **Total Submissions**: All-time form submission count
- **Monthly Activity**: Current month submission trends
- **Active Shares**: Currently accessible shared pages
- **Total Views**: Cumulative page view statistics

### Visual Analytics
- **Submission Charts**: Daily submission trends with interactive bars
- **Top Pages**: Ranked list of highest-performing pages
- **Recent Activity**: Real-time activity feed with timestamps
- **Export Options**: CSV, JSON, and SQL export formats

## 🔗 API Endpoints

### Page Management
```
GET  /includes/get_pages.php           - Retrieve page listings
POST /includes/enhanced_upload.php     - Upload files/ZIP archives
POST /includes/page_manager.php        - Page operations (CRUD)
```

### Form & Data Management
```
GET  /includes/get_forms.php           - Form definitions
GET  /includes/get_form_submissions.php - Submission data
POST /includes/export_submissions.php  - Data export
```

### User & Authentication
```
POST /includes/user_manager.php        - User operations
GET  /includes/get_analytics.php       - Analytics data
POST /login.php                        - Authentication
```

### Sharing & URLs
```
POST /includes/sharing_manager.php     - Share management
GET  /view.php?token=xxx              - Public page access
```

## ⚙️ Configuration Options

### Application Settings (Database)
```sql
-- Registration controls
allow_user_registration: boolean
require_registration_approval: boolean
registration_default_role: string
registration_welcome_message: string
```

### Environment Configuration
- `config/database.php` - Database connection settings
- `includes/deployment_config.php` - Environment-specific settings
- PHP settings for upload limits and memory

## 🐛 Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check credentials in config/database.php
# Verify MySQL service status
sudo systemctl status mysql

# Test connection
mysql -u username -p -h localhost
```

**File Upload Errors**
```bash
# Check directory permissions
ls -la uploads/
chmod 755 uploads/ -R

# Verify PHP settings
php -i | grep upload
```

**Permission Denied**
```bash
# Set proper ownership (VPS/Local)
chown -R www-data:www-data /path/to/webpage-manager/
chmod 644 *.php
chmod 755 uploads/
```

**Shared Hosting Specific Issues**

**Database Connection on Shared Hosting**
```php
// Common shared hosting database settings
private $host = 'localhost';  // Sometimes 'mysql.yourdomain.com'
private $db_name = 'username_dbname';  // Usually prefixed with username
private $username = 'username_dbuser'; // Usually prefixed with username
private $password = 'your_password';
```

**File Upload Issues on Shared Hosting**
- Check hosting provider's upload limits
- Verify `uploads/` directory exists and is writable
- Some hosts require 777 permissions: `chmod 777 uploads/`
- Contact hosting support if file uploads fail

**Database Import Errors**
- Use `database_setup_shared_hosting.sql` instead of `database_setup.sql`
- Import via phpMyAdmin in smaller chunks if file is too large
- Remove any `CREATE DATABASE` statements if they cause errors
- Contact hosting support for database import assistance

### Debug Mode
Enable detailed error reporting:
```php
// Add to top of index.php for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```

## 🚀 Deployment

### Production Checklist
- [ ] Update database credentials
- [ ] Set proper file permissions
- [ ] Configure web server (Apache/Nginx)
- [ ] Enable HTTPS/SSL
- [ ] Set up regular backups
- [ ] Configure error logging
- [ ] Test all functionality

### Recommended Server Configuration
```apache
# Apache .htaccess example
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

## 📝 Development

### Code Structure
```
webpage-manager/
├── assets/
│   ├── css/style.css          # Main stylesheet
│   └── js/app.js              # Main JavaScript application
├── config/
│   └── database.php           # Database configuration
├── includes/                  # Backend PHP files
│   ├── auth_manager.php       # Authentication system
│   ├── page_manager.php       # Page operations
│   ├── user_manager.php       # User management
│   └── [other APIs]
├── uploads/                   # User content storage
├── index.php                  # Main application
├── login.php                  # Authentication interface
├── view.php                   # Public page viewer
└── database_setup.sql         # Complete database schema
```

### Development Guidelines
- Use prepared statements for all database queries
- Implement proper error handling and logging
- Follow PSR-4 autoloading standards where applicable
- Maintain backward compatibility
- Write self-documenting code with comments
- Test thoroughly across different browsers

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For technical support:
1. Check this documentation
2. Review the troubleshooting section
3. Examine browser console for JavaScript errors
4. Check PHP error logs for server-side issues
5. Contact the development team

---

**Developed by Defabrika**
*A comprehensive solution for web page and form management*
